
### **AI UI Generation Prompt**

````
## HIGH-LEVEL GOAL

Create a responsive React component named `ProcessingScreen` for our AI JIRA Agent application. This screen is the main workspace where the user monitors the agent's progress in real-time and provides input at key steps. The component must be built using TypeScript, Tailwind CSS, and shadcn/ui components.

---

## DETAILED, STEP-BY-STEP INSTRUCTIONS

1.  **Component and File Structure:** Create a new React functional component in a file named `ProcessingScreen.tsx`.
2.  **Layout:** Implement a responsive two-column layout. On desktop, the left column should take up 1/4 of the width and the right column should take up 3/4. On mobile screens (below 768px), the layout should stack into a single column with the left column's content appearing on top.
3.  **Left Column (Stepper):**
    * Create a vertical stepper component in the left column.
    * The stepper must display the following stages: "Analyzing Requirement", "Identifying Stories", "Drafting Plan", "Generating Tickets", and "Complete".
    * The component should accept a prop `currentStage` to indicate which stage is currently active.
4.  **Right Column (Preview & Controls):**
    * Create a large panel in the right column to act as the "Real-time Preview". It should have a styled border and padding.
    * Below the preview panel, create a footer section containing three `shadcn/ui` buttons:
        * A secondary button labeled "Pause".
        * A primary button labeled "Approve Plan".
        * A primary button labeled "Commit to JIRA".
5.  **Iconography:** Use icons from the `lucide-react` library for the stepper stages and buttons where appropriate (e.g., a checkmark for completed steps).

---

## CODE EXAMPLES, DATA STRUCTURES & CONSTRAINTS

* **Technology Stack:** This component MUST be built with React, TypeScript, Vite, Tailwind CSS, and `shadcn/ui`.
* **Styling:**
    * All styling MUST be done with Tailwind CSS utility classes.
    * The primary color for active elements (like the active step or primary buttons) is `#2563EB`.
    * Use the "Inter" font for all text.
    * All spacing (padding, margins) MUST adhere to an 8-point grid system.
* **Component Imports (Example):**
    ```typescript
    import { Button } from "@/components/ui/button";
    import { CheckCircle } from "lucide-react";
    ```
* **Props Interface (Data Structure):** The component should expect the following props:
    ```typescript
    type ProcessingScreenProps = {
      currentStage: 'analyzing' | 'identifying' | 'drafting' | 'generating' | 'complete';
      previewContent: string; // The text/markdown to display in the preview panel
      isPaused: boolean;
      onPauseToggle: () => void;
      onApprove: () => void;
      onCommit: () => void;
    };
    ```
* **Constraints:**
    * **DO NOT** implement any state management inside this component (e.g., `useState`). It should be a purely presentational component that receives its state via props.
    * **DO NOT** implement any API calls. The `on...` functions are placeholders for parent components to handle logic.

---

## STRICT SCOPE

You should ONLY create the `ProcessingScreen.tsx` component file and its contents. Do not create any other components or modify any other files. The component should be self-contained.
````
