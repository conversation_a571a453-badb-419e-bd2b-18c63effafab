Of course. Here is the complete Fullstack Architecture document.

-----

# **AI JIRA Agent Fullstack Architecture Document**

## **Introduction**

[cite\_start]This document outlines the complete fullstack architecture for the AI JIRA Agent, including backend systems, frontend implementation, and their integration[cite: 881]. [cite\_start]It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack[cite: 881].

### **Starter Template or Existing Project**

[cite\_start]The project will be built using a **Turborepo starter template** to manage a monorepo containing a **Next.js frontend** and a **Strapi backend**[cite: 884]. [cite\_start]This approach provides best-in-class tooling, fast build times, and a scalable foundation[cite: 884].

### **Change Log**

| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-07-30 | 1.0 | Initial draft and section approvals. | <PERSON>, Architect |

-----

## **High Level Architecture**

### **Technical Summary**

[cite\_start]This project implements a full-stack application within a **Turborepo monorepo**[cite: 891]. [cite\_start]The frontend is a **pure React Single Page Application (SPA)** built with `shadcn/ui` and `Tailwind CSS`[cite: 891]. [cite\_start]The backend is a self-hosted **Strapi Headless CMS**, containerized with **Docker**[cite: 891]. [cite\_start]The entire application stack is managed via `Docker-compose` for portability and ease of local development[cite: 891].

### **Platform and Infrastructure Choice**

  * [cite\_start]**Platform**: **Self-Hosted via Docker**[cite: 896].
  * **Key Services**:
      * [cite\_start]**Docker-compose**: Manages the multi-container application stack[cite: 896].
      * [cite\_start]**Database**: Defaulting to **SQLite** for simplicity, with the ability to switch to **PostgreSQL** for production[cite: 896].

### **High Level Architecture Diagram**

```mermaid
graph TD
    subgraph "User's Browser"
        A[React SPA <br> shadcn/ui + Tailwind CSS]
        MCP_Client[use-mcp Client Library]
        A -- "Contains" --> MCP_Client
    end

    subgraph "Self-Hosted Docker Environment"
        B[Strapi Backend Container]
        C[Database Container <br> SQLite / PostgreSQL]
        D[elizaOS Agent Plugin]
        B -- "Contains" --> D
        B -- "CRUD Operations" --> C
    end
    
    subgraph "External Services"
        E[JIRA MCP Server]
    end

    A -- "REST/GraphQL API Calls" --> B
    MCP_Client -- "Calls Tool" --> E
```

### **Architectural Patterns**

  * [cite\_start]**Single Page Application (SPA)**: A dynamic, client-side rendered React application[cite: 903].
  * [cite\_start]**Headless CMS**: Using Strapi to decouple the backend from the frontend[cite: 903].
  * [cite\_start]**Plugin Architecture**: The core AI logic will be encapsulated as a Strapi plugin[cite: 903].
  * [cite\_start]**Containerization**: The entire application will be containerized using Docker[cite: 903].

-----

## **Tech Stack**

[cite\_start]This table serves as the single source of truth for all technologies, libraries, and versions used in the project[cite: 906, 907].

| Category | Technology |
| :--- | :--- |
| **Frontend Framework** | [cite\_start]React (with Vite) [cite: 910] |
| [cite\_start]**UI Component Library** | shadcn/ui [cite: 910] |
| **CSS Framework** | [cite\_start]Tailwind CSS [cite: 912] |
| **Backend Framework**| [cite\_start]Strapi [cite: 910] |
| **Database** | [cite\_start]SQLite / PostgreSQL [cite: 910] |
| **Monorepo Tool** | [cite\_start]Turborepo [cite: 912] |
| **IaC / Runtime**| [cite\_start]Docker-compose [cite: 912] |
| **CI/CD** | [cite\_start]GitHub Actions [cite: 912] |

-----

## **Data Models**

[cite\_start]The application's data structure is defined by the following models[cite: 913]:

  * [cite\_start]**User (Built-in)**: Strapi's user model will handle authentication[cite: 913].
  * [cite\_start]**GenerationJob**: Tracks a single requirement-to-ticket generation process[cite: 913].
  * [cite\_start]**GeneratedTicket**: Stores a reference to a JIRA ticket created by a job[cite: 913].

-----

## **API Specification**

[cite\_start]A REST API, defined with OpenAPI 3.0, will serve as the contract between the frontend and the Strapi backend[cite: 918]. [cite\_start]Core endpoints include `POST /generation-jobs`, `GET /generation-jobs/{id}/status`, and `POST /generation-jobs/{id}/commit`[cite: 918].

-----

## **Components**

  * [cite\_start]**React Frontend (GUI)**: Renders the UI and manages user interactions[cite: 924].
  * [cite\_start]**Strapi Backend (API & CMS)**: Provides the main API, handles auth, and persists data[cite: 924].
  * [cite\_start]**elizaOS Agent Plugin**: The "brain" containing the core ticket generation logic[cite: 924].
  * [cite\_start]**JIRA Integration Service**: A service within the backend that handles all communication with the external JIRA MCP Server[cite: 924].

-----

## **External APIs**

[cite\_start]The application's primary external dependency is the **JIRA MCP Server**, which will be used for all ticket creation operations[cite: 929]. [cite\_start]Connection will be secured with OAuth 2.0[cite: 929].

-----

## **Core Workflows**

[cite\_start]A sequence diagram has been defined detailing the end-to-end flow from a user submitting a requirement to the final confirmation of JIRA ticket creation, illustrating the interactions between all internal and external components[cite: 933].

-----

## **Database Schema**

[cite\_start]A concrete SQL DDL has been defined for the `generation_jobs` and `generated_tickets` tables, including columns, types, foreign keys, and indexes for performance[cite: 935].

-----

## **Frontend & Backend Architecture**

[cite\_start]Specific, granular architectural patterns for both the React frontend and the Strapi backend have been defined, including folder structures, state management strategies, routing, data access layers, and authentication flows[cite: 937, 947].

-----

## **Unified Project Structure**

[cite\_start]A detailed ASCII diagram of the Turborepo monorepo has been created, showing the placement of the `frontend` and `backend` apps, `shared-types` packages, and root-level Docker configurations[cite: 957].

-----

## **Development & Deployment**

[cite\_start]Workflows for both local development (using `docker-compose up`) and production deployment (using a `GitHub Actions` CI/CD pipeline to build and push Docker images) have been defined[cite: 970, 978].

-----

## **Security, Performance, Testing, and Standards**

[cite\_start]Comprehensive strategies for security, performance, testing, error handling, monitoring, and a minimal set of critical coding standards have been defined to ensure a robust and maintainable application[cite: 982, 985, 987, 994, 998, 1002].

-----

## **Checklist Results Report**

[cite\_start]A preliminary review against the `architect-checklist` shows that this document is comprehensive[cite: 1007]. [cite\_start]A full, detailed checklist execution is recommended as a formal quality gate before development begins[cite: 1007].

