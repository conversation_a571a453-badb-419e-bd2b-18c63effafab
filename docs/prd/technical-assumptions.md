# **Technical Assumptions**

## **Repository Structure: Monorepo**
The project will be housed in a single monorepo to facilitate sharing of types and logic between the web-based GUI (frontend) and the agent's core logic (backend).

## **Service Architecture: Headless CMS (Strapi)**
The project's backend will be built using Strapi, a leading open-source headless CMS. This provides a robust, pre-built foundation for our API layer, authentication, and permissions. The agent's custom logic, built with `elizaOS/eliza`, will be integrated as a custom plugin or service within the Strapi application. This approach accelerates development by leveraging Strapi's core CMS features while still allowing for fully custom, complex business logic.

## **Testing Requirements: Unit + Integration**
The testing strategy will include unit tests for individual components and functions, as well as integration tests to verify the connections between the GUI, the agent logic, and the JIRA integration.

## **Additional Technical Assumptions and Requests**
* **Agent Framework:** The agent will be built using the `elizaOS/eliza` framework.
* **Core Libraries:** The project will utilize `modelcontextprotocol/use-mcp` for standardized AI-to-tool communication and `bmadcode/BMAD-METHOD` as the knowledge base for generation logic.
* **JIRA Integration:** The connection to JIRA must be made via existing MCP servers, using the OAuth 2.0 protocol.
