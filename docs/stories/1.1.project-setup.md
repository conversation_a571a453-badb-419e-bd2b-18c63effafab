# Story 1.1: Project Setup and Monorepo Configuration

## Status
Draft

## Story
**As a** developer,
**I want** a properly configured monorepo with frontend and backend applications,
**so that** I can develop both parts of the system with shared types and consistent tooling.

## Acceptance Criteria
1. Monorepo is created with proper workspace configuration (package.json workspaces or similar)
2. Frontend application is scaffolded using React with TypeScript
3. Backend application is scaffolded using Strapi with TypeScript
4. Shared package is created for common types and utilities
5. ESLint, Prettier, and TypeScript configurations are consistent across all packages
6. Package scripts are configured for development, build, and test commands
7. Git repository is initialized with appropriate .gitignore files
8. README.md includes setup and development instructions

## Tasks / Subtasks
- [ ] Task 1: Initialize Turborepo monorepo structure (AC: 1)
  - [ ] Install Turborepo CLI globally
  - [ ] Create new Turborepo project with appropriate template
  - [ ] Configure root package.json with workspaces
  - [ ] Set up Turborepo configuration (turbo.json)
- [ ] Task 2: Set up React frontend application (AC: 2)
  - [ ] Create apps/web directory
  - [ ] Initialize React app with Vite and TypeScript
  - [ ] Configure Vite build configuration
  - [ ] Install and configure shadcn/ui component library
  - [ ] Install and configure Tailwind CSS
- [ ] Task 3: Set up Strapi backend application (AC: 3)
  - [ ] Create apps/backend directory
  - [ ] Initialize Strapi application with TypeScript template
  - [ ] Configure Strapi for development environment
  - [ ] Set up basic Strapi plugins (Users & Permissions)
- [ ] Task 4: Create shared packages (AC: 4)
  - [ ] Create packages/shared directory structure
  - [ ] Set up TypeScript configuration for shared types
  - [ ] Create basic type definitions for User, Project entities
  - [ ] Configure package exports and imports
- [ ] Task 5: Configure development tooling (AC: 5, 6)
  - [ ] Set up ESLint configuration at root level
  - [ ] Configure Prettier for consistent code formatting
  - [ ] Create TypeScript configuration files for each package
  - [ ] Configure package.json scripts for dev, build, test, lint
  - [ ] Set up Turborepo pipeline for build orchestration
- [ ] Task 6: Initialize Git repository and documentation (AC: 7, 8)
  - [ ] Initialize Git repository with appropriate .gitignore
  - [ ] Create comprehensive README.md with setup instructions
  - [ ] Document development workflow and commands
  - [ ] Create environment variable templates (.env.example)

## Dev Notes

### Previous Story Insights
No previous story exists - this is the foundational story for the project.

### Tech Stack Information
[Source: architecture/tech-stack.md]
- **Frontend Framework:** React (with Vite)
- **UI Component Library:** shadcn/ui
- **CSS Framework:** Tailwind CSS
- **Backend Framework:** Strapi
- **Database:** SQLite / PostgreSQL
- **Monorepo Tool:** Turborepo
- **IaC / Runtime:** Docker-compose
- **CI/CD:** GitHub Actions

### Project Structure Requirements
[Source: architecture/unified-project-structure.md, architecture/introduction.md]
- Project will use Turborepo starter template
- Monorepo containing Next.js frontend and Strapi backend
- Structure should include apps/ and packages/ directories
- Shared types package for common interfaces
- Docker configurations at root level

### Technical Constraints
[Source: prd/technical-assumptions.md]
- Monorepo structure required for sharing types and logic
- Strapi as headless CMS foundation
- Integration with elizaOS/eliza framework (future stories)
- MCP (Model Context Protocol) integration required
- OAuth 2.0 for JIRA integration

### File Locations
Based on project structure analysis:
- Root: `/` (project root)
- Frontend app: `/apps/web/`
- Backend app: `/apps/backend/`
- Shared packages: `/packages/shared/`
- Configuration: Root level turbo.json, package.json
- Documentation: `/README.md`, `/docs/`

### Testing Requirements
[Source: prd/technical-assumptions.md]
- Unit tests for individual components and functions
- Integration tests for GUI, agent logic, and JIRA integration
- Testing framework setup should be included in monorepo configuration

### Technical Implementation Notes
- Use Turborepo for monorepo management and build orchestration
- React with Vite for faster development and build times
- TypeScript throughout for type safety
- shadcn/ui provides accessible, customizable components
- Strapi provides authentication, API, and admin interface foundation
- Package manager: pnpm (as indicated in existing package.json)

## Testing
### Testing Standards
[Source: architecture/security-performance-testing-and-standards.md]
- Comprehensive testing strategies should be implemented
- Test file locations should follow standard conventions
- Testing frameworks: Jest for unit tests, testing-library for React components
- Integration tests for API endpoints and database interactions

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-07-30 | 1.0 | Initial story creation | SM Bob |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled by dev agent*

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
*To be filled by dev agent*

### File List
*To be filled by dev agent*

## QA Results
*Results from QA Agent review will be populated here*
