# **Data Models**

[cite\_start]The application's data structure is defined by the following models[cite: 913]:

  * [cite\_start]**User (Built-in)**: Strapi's user model will handle authentication[cite: 913].
  * [cite\_start]**GenerationJob**: Tracks a single requirement-to-ticket generation process[cite: 913].
  * [cite\_start]**GeneratedTicket**: Stores a reference to a JIRA ticket created by a job[cite: 913].

-----
