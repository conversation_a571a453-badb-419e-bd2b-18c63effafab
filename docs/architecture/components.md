# **Components**

  * [cite\_start]**React Frontend (GUI)**: Renders the UI and manages user interactions[cite: 924].
  * [cite\_start]**Strapi Backend (API & CMS)**: Provides the main API, handles auth, and persists data[cite: 924].
  * [cite\_start]**elizaOS Agent Plugin**: The "brain" containing the core ticket generation logic[cite: 924].
  * [cite\_start]**JIRA Integration Service**: A service within the backend that handles all communication with the external JIRA MCP Server[cite: 924].

-----
