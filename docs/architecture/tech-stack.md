# **Tech Stack**

[cite\_start]This table serves as the single source of truth for all technologies, libraries, and versions used in the project[cite: 906, 907].

| Category | Technology |
| :--- | :--- |
| **Frontend Framework** | [cite\_start]React (with Vite) [cite: 910] |
| [cite\_start]**UI Component Library** | shadcn/ui [cite: 910] |
| **CSS Framework** | [cite\_start]Tailwind CSS [cite: 912] |
| **Backend Framework**| [cite\_start]Strapi [cite: 910] |
| **Database** | [cite\_start]SQLite / PostgreSQL [cite: 910] |
| **Monorepo Tool** | [cite\_start]Turborepo [cite: 912] |
| **IaC / Runtime**| [cite\_start]Docker-compose [cite: 912] |
| **CI/CD** | [cite\_start]GitHub Actions [cite: 912] |

-----
