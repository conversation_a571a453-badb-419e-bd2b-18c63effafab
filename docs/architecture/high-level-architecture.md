# **High Level Architecture**

## **Technical Summary**

[cite\_start]This project implements a full-stack application within a **Turborepo monorepo**[cite: 891]. [cite\_start]The frontend is a **pure React Single Page Application (SPA)** built with `shadcn/ui` and `Tailwind CSS`[cite: 891]. [cite\_start]The backend is a self-hosted **Strapi Headless CMS**, containerized with **Docker**[cite: 891]. [cite\_start]The entire application stack is managed via `Docker-compose` for portability and ease of local development[cite: 891].

## **Platform and Infrastructure Choice**

  * [cite\_start]**Platform**: **Self-Hosted via Docker**[cite: 896].
  * **Key Services**:
      * [cite\_start]**Docker-compose**: Manages the multi-container application stack[cite: 896].
      * [cite\_start]**Database**: Defaulting to **SQLite** for simplicity, with the ability to switch to **PostgreSQL** for production[cite: 896].

## **High Level Architecture Diagram**

```mermaid
graph TD
    subgraph "User's Browser"
        A[React SPA <br> shadcn/ui + Tailwind CSS]
        MCP_Client[use-mcp Client Library]
        A -- "Contains" --> MCP_Client
    end

    subgraph "Self-Hosted Docker Environment"
        B[Strapi Backend Container]
        C[Database Container <br> SQLite / PostgreSQL]
        D[elizaOS Agent Plugin]
        B -- "Contains" --> D
        B -- "CRUD Operations" --> C
    end
    
    subgraph "External Services"
        E[JIRA MCP Server]
    end

    A -- "REST/GraphQL API Calls" --> B
    MCP_Client -- "Calls Tool" --> E
```

## **Architectural Patterns**

  * [cite\_start]**Single Page Application (SPA)**: A dynamic, client-side rendered React application[cite: 903].
  * [cite\_start]**Headless CMS**: Using Strapi to decouple the backend from the frontend[cite: 903].
  * [cite\_start]**Plugin Architecture**: The core AI logic will be encapsulated as a Strapi plugin[cite: 903].
  * [cite\_start]**Containerization**: The entire application will be containerized using Docker[cite: 903].

-----
